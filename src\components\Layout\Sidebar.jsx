import React, { useState } from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import {
  HiHome,
  HiShoppingCart,
  HiChartBar,
  HiClipboardList,
  HiCog,
  HiX,
  HiChevronDown,
  HiChevronRight,
  HiColorSwatch,
  HiRefresh
} from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HiHome },
  { name: 'Purchases', href: '/purchases', icon: HiShoppingCart },
  { name: 'Usage', href: '/usage', icon: HiChartBar },
  { name: 'History', href: '/history', icon: HiClipboardList },
]

const settingsSubmenu = [
  { name: 'General Settings', href: '/settings?section=general', icon: HiCog },
  { name: 'Appearance', href: '/settings?section=appearance', icon: HiColorSwatch },
  { name: 'Reset Options', href: '/settings?section=reset', icon: HiRefresh },
]

function Sidebar({ isOpen, onClose }) {
  const { theme, currentTheme } = useTheme()
  const location = useLocation()
  const [settingsOpen, setSettingsOpen] = useState(false)

  // Helper function to get appropriate background for sidebar with glassmorphism
  const getSidebarBackground = () => {
    if (currentTheme === 'dark') {
      return 'bg-gray-900/85 backdrop-blur-xl border-gray-700/50'
    }
    // For non-dark themes, use lighter theme color with glassmorphism
    return `${theme.dark}/60 backdrop-blur-xl border-white/20`
  }

  // Helper function to get appropriate text color for sidebar
  const getSidebarTextColor = () => {
    if (currentTheme === 'dark') {
      return theme.text // Keep existing text color for dark mode
    }
    // For non-dark themes, use white text on semi-transparent background
    return 'text-white'
  }

  // Helper function to get hover background
  const getHoverBackground = () => {
    if (currentTheme === 'dark') {
      return 'bg-white/10 backdrop-blur-sm'
    }
    return 'bg-white/15 backdrop-blur-sm'
  }

  // Helper function to get active background
  const getActiveBackground = () => {
    if (currentTheme === 'dark') {
      return 'bg-white/15 backdrop-blur-md'
    }
    return 'bg-white/25 backdrop-blur-md'
  }

  return (
    <>
      {/* Desktop sidebar */}
      <div className={`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${getSidebarBackground()} border-r shadow-2xl`}>
        <div className="flex flex-col w-full">
          {/* Menu section */}
          <div className={`flex items-center justify-center h-16 px-4 border-b border-white/15`}>
            <h2 className={`text-xl font-bold ${getSidebarTextColor()} tracking-wider drop-shadow-sm`}>MENU</h2>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${
                    isActive
                      ? `${getActiveBackground()} ${getSidebarTextColor()} shadow-lg border border-white/20`
                      : `${getSidebarTextColor()} hover:${getHoverBackground()} hover:shadow-md hover:border hover:border-white/10`
                  }`
                }
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </NavLink>
            ))}

            {/* Settings with dropdown */}
            <div className="space-y-1">
              <button
                onClick={() => setSettingsOpen(!settingsOpen)}
                className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${
                  location.pathname.includes('/settings')
                    ? `${getActiveBackground()} ${getSidebarTextColor()} shadow-lg border border-white/20`
                    : `${getSidebarTextColor()} hover:${getHoverBackground()} hover:shadow-md hover:border hover:border-white/10`
                }`}
              >
                <div className="flex items-center">
                  <HiCog className="mr-3 h-5 w-5" />
                  Settings
                </div>
                {settingsOpen ? (
                  <HiChevronDown className="h-4 w-4" />
                ) : (
                  <HiChevronRight className="h-4 w-4" />
                )}
              </button>

              {/* Settings submenu */}
              {settingsOpen && (
                <div className="ml-4 space-y-1 mt-2">
                  {settingsSubmenu.map((item) => (
                    <NavLink
                      key={item.name}
                      to={item.href}
                      className={({ isActive }) =>
                        `flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${
                          isActive
                            ? `bg-white/20 backdrop-blur-sm ${getSidebarTextColor()} shadow-md border border-white/15`
                            : `${getSidebarTextColor()} opacity-85 hover:bg-white/10 hover:backdrop-blur-sm hover:opacity-100 hover:shadow-sm`
                        }`
                      }
                    >
                      <item.icon className="mr-3 h-4 w-4" />
                      {item.name}
                    </NavLink>
                  ))}
                </div>
              )}
            </div>
          </nav>
        </div>
      </div>

      {/* Mobile sidebar */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${getSidebarBackground()} transform transition-transform duration-300 ease-in-out shadow-2xl ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Header with close button */}
          <div className={`flex items-center justify-between h-16 px-4 border-b border-white/15`}>
            <div className="flex items-center">
              <h2 className={`text-xl font-bold ${getSidebarTextColor()} tracking-wider drop-shadow-sm`}>MENU</h2>
            </div>
            <button
              onClick={onClose}
              className={`p-2 rounded-xl ${getSidebarTextColor()} hover:bg-white/15 hover:backdrop-blur-sm transition-all duration-300 shadow-sm`}
            >
              <HiX className="h-6 w-6" />
            </button>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={onClose}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${
                    isActive
                      ? `${getActiveBackground()} ${getSidebarTextColor()} shadow-lg border border-white/20`
                      : `${getSidebarTextColor()} hover:${getHoverBackground()} hover:shadow-md hover:border hover:border-white/10`
                  }`
                }
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </NavLink>
            ))}

            {/* Settings with dropdown */}
            <div className="space-y-1">
              <button
                onClick={() => setSettingsOpen(!settingsOpen)}
                className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${
                  location.pathname.includes('/settings')
                    ? `${getActiveBackground()} ${getSidebarTextColor()} shadow-lg border border-white/20`
                    : `${getSidebarTextColor()} hover:${getHoverBackground()} hover:shadow-md hover:border hover:border-white/10`
                }`}
              >
                <div className="flex items-center">
                  <HiCog className="mr-3 h-5 w-5" />
                  Settings
                </div>
                {settingsOpen ? (
                  <HiChevronDown className="h-4 w-4" />
                ) : (
                  <HiChevronRight className="h-4 w-4" />
                )}
              </button>

              {/* Settings submenu */}
              {settingsOpen && (
                <div className="ml-4 space-y-1 mt-2">
                  {settingsSubmenu.map((item) => (
                    <NavLink
                      key={item.name}
                      to={item.href}
                      onClick={onClose}
                      className={({ isActive }) =>
                        `flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${
                          isActive
                            ? `bg-white/20 backdrop-blur-sm ${getSidebarTextColor()} shadow-md border border-white/15`
                            : `${getSidebarTextColor()} opacity-85 hover:bg-white/10 hover:backdrop-blur-sm hover:opacity-100 hover:shadow-sm`
                        }`
                      }
                    >
                      <item.icon className="mr-3 h-4 w-4" />
                      {item.name}
                    </NavLink>
                  ))}
                </div>
              )}
            </div>
          </nav>
        </div>
      </div>
    </>
  )
}

export default Sidebar
