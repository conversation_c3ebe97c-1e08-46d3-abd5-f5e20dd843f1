import React from 'react'
import { useTheme } from '../../context/ThemeContext'

function Logo({ size = 'md', animated = true, showText = true }) {
  const { theme } = useTheme()
  const sizes = {
    sm: {
      logo: 'h-18 w-auto max-w-24',
      text: 'text-sm'
    },
    md: {
      logo: 'h-20 w-auto max-w-28',
      text: 'text-lg'
    },
    lg: {
      logo: 'h-22 w-auto max-w-30',
      text: 'text-xl'
    },
    xl: {
      logo: 'h-24 w-auto max-w-32',
      text: 'text-2xl'
    }
  }

  const currentSize = sizes[size] || sizes.md

  return (
    <div className="flex items-center gap-3">
      {/* Custom Logo */}
      <img
        src="/oie_transparent (1).png"
        alt="Prepaid User Electricity Logo"
        className={`${currentSize.logo} object-contain`}
      />

      {/* App name */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${currentSize.text} font-black ${theme.text} leading-tight`}>
            Prepaid User
          </h1>
          <p className={`text-base font-bold ${theme.textSecondary} tracking-wider leading-tight`}>
            Electricity
          </p>
        </div>
      )}
    </div>
  )
}

export default Logo
