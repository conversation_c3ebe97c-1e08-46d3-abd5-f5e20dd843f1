import React from 'react'
import { HiMenu } from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'
import { useApp } from '../../context/AppContext'
import Logo from '../Common/Logo'

function Header({ onMenuClick }) {
  const { theme, currentTheme } = useTheme()
  const { state } = useApp()

  // Helper function to get appropriate background for header with glassmorphism
  const getHeaderBackground = () => {
    if (currentTheme === 'dark') {
      return 'bg-gray-900/80 backdrop-blur-xl border-gray-700/50'
    }
    // For non-dark themes, use lighter theme color with glassmorphism
    return `${theme.dark}/70 backdrop-blur-xl border-white/20`
  }

  // Helper function to get appropriate text color for header
  const getHeaderTextColor = () => {
    if (currentTheme === 'dark') {
      return theme.text // Keep existing text color for dark mode
    }
    // For non-dark themes, use white text on semi-transparent background
    return 'text-white'
  }

  return (
    <header className={`${getHeaderBackground()} border-b px-4 py-3 flex items-center justify-between shadow-xl`}>
      {/* Left side - Menu button and logo */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onMenuClick}
          className={`lg:hidden p-2 rounded-xl bg-white/15 backdrop-blur-sm text-white hover:bg-white/25 transition-all duration-300 shadow-lg`}
        >
          <HiMenu className="h-6 w-6" />
        </button>

        <div className="flex items-center">
          <Logo size="md" animated={true} showText={false} />
        </div>
      </div>

      {/* Right side - Current units display */}
      <div className={`hidden sm:flex items-center space-x-4 bg-white/10 backdrop-blur-md rounded-xl px-4 py-2 border border-white/20 shadow-lg`}>
        <div className="text-right">
          <p className={`text-sm ${getHeaderTextColor()} opacity-90`}>Current Units</p>
          <p className={`text-lg font-bold ${getHeaderTextColor()}`}>
            {state.currentUnits.toFixed(2)}
          </p>
        </div>
        <div className={`w-3 h-3 rounded-full bg-white/70 pulse-glow shadow-sm`} />
      </div>
    </header>
  )
}

export default Header
