import React from 'react'
import { HiMenu } from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'
import { useApp } from '../../context/AppContext'
import Logo from '../Common/Logo'

function Header({ onMenuClick }) {
  const { theme, currentTheme } = useTheme()
  const { state } = useApp()

  // Helper function to get appropriate background for header
  const getHeaderBackground = () => {
    if (currentTheme === 'dark') {
      return theme.darker // Use darker shade for dark mode
    }
    // For non-dark themes, use darker theme color for more prominence
    return theme.darker
  }

  // Helper function to get appropriate text color for header
  const getHeaderTextColor = () => {
    if (currentTheme === 'dark') {
      return theme.text // Keep existing text color for dark mode
    }
    // For non-dark themes, use white text on darker background
    return 'text-white'
  }

  return (
    <header className={`${getHeaderBackground()} border-b px-4 py-3 flex items-center justify-between shadow-lg border-gray-700`}>
      {/* Left side - Menu button and logo */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onMenuClick}
          className={`lg:hidden p-2 rounded-md bg-white/20 text-white hover:bg-white/30 transition-all duration-200`}
        >
          <HiMenu className="h-6 w-6" />
        </button>

        <div className="flex items-center">
          <Logo size="md" animated={true} showText={false} />
        </div>
      </div>

      {/* Right side - Current units display */}
      <div className={`hidden sm:flex items-center space-x-4 bg-black/20 rounded-lg px-4 py-2 border border-white/20 backdrop-blur-sm`}>
        <div className="text-right">
          <p className={`text-sm ${getHeaderTextColor()} opacity-80`}>Current Units</p>
          <p className={`text-lg font-bold ${getHeaderTextColor()}`}>
            {state.currentUnits.toFixed(2)}
          </p>
        </div>
        <div className={`w-3 h-3 rounded-full bg-white/60 pulse-glow`} />
      </div>
    </header>
  )
}

export default Header
